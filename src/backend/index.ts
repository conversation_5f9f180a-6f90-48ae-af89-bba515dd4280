// Import Fastify and plugins
import Fastify from 'fastify';
// Handlebars removed - pure Vue SPA now
import path from 'path';
// Vite helpers removed - assets handled directly in SPA route
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import cors from '@fastify/cors';
import cookie from '@fastify/cookie';
import formbody from '@fastify/formbody';
import fastifySwagger from '@fastify/swagger';
import fastifyStatic from '@fastify/static';
import fastifySwaggerUI from '@fastify/swagger-ui';

// Import routes
import { emailRoutes } from './routes/email.js';
import { webhookRoutes } from './routes/webhook.js';
import { domainsRoutes } from './routes/domains.routes.js';
import { userWebhooksRoutes } from './routes/user-webhooks.routes.js';
import { userAliasRoutes } from './routes/user-aliases.routes.js';
import { dashboardRoutes } from './routes/dashboard.js';
import authRoutes from './routes/auth.js';
import { templateRoutes } from './routes/templates.js';
import { billingRoutes } from './routes/billing.js';
import { logsRoutes } from './routes/logs.routes.js';
import { apiKeyRoutes } from './routes/api-keys.routes.js';
import { mollieWebhookRoutes } from './routes/mollie-webhooks.routes.js';
import { profileRoutes } from './routes/profile.js';

// Import services and utilities
import { initializeQueue } from './services/queue.js';
import { connectDatabase, disconnectDatabase, checkDatabaseHealth } from './lib/prisma.js';
import { VerificationWorker } from './services/verification-worker.js';
import { UsageResetService } from './services/billing/usage-reset.service.js';
import { webSocketService } from './services/websocket.service.js';
import { env } from './config/env.js'; // This already handles dotenv.config()
import { openApiSpecification } from './openapi-spec.js';
import { registerCommonSchemas } from './schemas/common.js';
import { filterPublicRoutes } from './lib/swagger-utils.js';
import { allSchemas } from './schemas/openapi-schemas.js';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Handlebars helpers removed - pure Vue SPA now

// Initialize Fastify with the shared logger instance
export const fastify = Fastify({
  logger: {
    level: env.NODE_ENV === 'development' ? 'debug' : 'info',
    transport: env.NODE_ENV === 'development' ? {
      target: 'pino-pretty',
      options: {
        colorize: true,
        translateTime: 'SYS:standard',
        ignore: 'pid,hostname',
      },
    } : undefined,
    redact: ['email.from.value', 'email.to.value', 'email.subject'],
  },
  ajv: {
    customOptions: {
      strict: false,
      keywords: ['example'], // allows "example" keyword
    }
  },
});

// Initialize verification worker and usage reset service
const verificationWorker = new VerificationWorker();
const usageResetService = new UsageResetService();

async function start() {
  try {
    // Connect to database
    await connectDatabase();

    // Register CORS with proper credentials support
    await fastify.register(cors, {
      origin: (origin, callback) => {
        // Allow requests from same origin (SPA) and development
        if (!origin ||
            origin === 'https://emailconnect.eu' ||
            origin === 'http://localhost:5173' ||
            origin === 'http://localhost:3000' ||
            origin.includes('localhost')) {
          callback(null, true);
        } else {
          callback(new Error('Not allowed by CORS'), false);
        }
      },
      credentials: true, // Enable credentials (cookies) support
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key']
    });
    
    // Template engine removed - pure Vue SPA now

    // Determine if we're in development or production
    const isDev = __dirname.includes('src/backend');
    const rootDir = isDev ? path.join(__dirname, '../..') : path.join(__dirname, '../..');

    // Serve Vue frontend assets with proper MIME types
    await fastify.register(fastifyStatic, {
      root: path.join(rootDir, 'dist/frontend/assets'),
      prefix: '/assets/',
      decorateReply: false
    });

    // Serve Vue SPA files (for sendFile functionality)
    await fastify.register(fastifyStatic, {
      root: path.join(rootDir, 'dist/frontend'),
      prefix: '/spa/',
      decorateReply: true,  // This enables reply.sendFile()
      serve: false  // Don't serve directory, just enable sendFile
    });

    // Serve public files (including custom CSS)
    await fastify.register(fastifyStatic, {
      root: path.join(rootDir, 'public'),
      prefix: '/',
      decorateReply: false
    });

    // Add all schemas (including admin) to Fastify for $ref resolution
    // This ensures all routes can find their referenced schemas
    for (const [id, schema] of Object.entries(allSchemas)) {
      fastify.addSchema({ ...schema, $id: id });
    }
    fastify.log.info(`Registered ${Object.keys(allSchemas).length} schemas from OpenAPI spec`);
    
    // Register common schemas
    await registerCommonSchemas(fastify);
    fastify.log.info('Common schemas registered via registerCommonSchemas.');

    // Register form body parser
    await fastify.register(formbody);

    // Register cookie plugin
    await fastify.register(cookie, {
      secret: env.ADMIN_JWT_SECRET, // for signed cookies
      parseOptions: {} // options for parsing cookies
    });

    // Register Swagger in static mode to avoid auto-generation from routes
    await fastify.register(fastifySwagger, {
      mode: 'static',
      specification: {
        document: openApiSpecification,
      },
    });

    // Register Swagger UI
    await fastify.register(fastifySwaggerUI, {
      routePrefix: '/docs',
      uiConfig: {
        docExpansion: 'list',
        deepLinking: true,
        displayRequestDuration: true,
        filter: true,
        showExtensions: true,
        showCommonExtensions: true,
        tryItOutEnabled: true,
        supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch'],
        validatorUrl: null, // Disable online validator
      },
      staticCSP: true,
      transformStaticCSP: (header) => header,
      transformSpecification: (swaggerObject) => {
        // Apply filtering to show only public routes
        return filterPublicRoutes(swaggerObject);
      }
    });

    // Initialize Redis queue
    await initializeQueue();

    // Initialize WebSocket service
    webSocketService.initialize(fastify);
    fastify.log.info('✅ WebSocket service initialized');

    // Add global scope validation hook (runs before schema validation)
    fastify.addHook('onRequest', async (request, reply) => {
      // Only check API routes
      if (!request.url.startsWith('/api/')) {
        return;
      }

      // Only validate API key requests (not cookie-based)
      const apiKey = request.headers['x-api-key'] as string;
      if (!apiKey) {
        return; // Let normal auth middleware handle this
      }

      try {
        const { ApiKeyService } = await import('./services/auth/api-key.service.js');
        const { ScopeValidatorService } = await import('./services/auth/scope-validator.service.js');
        const apiKeyService = new ApiKeyService();

        const verifyResult = await apiKeyService.verifyApiKey(apiKey);
        if (!verifyResult.success) {
          return reply.status(401).send({ error: 'Unauthorized: Invalid API key' });
        }

        // Validate scopes for this request
        const scopeValidation = ScopeValidatorService.validateRequest(verifyResult.scopes!, request);
        if (!scopeValidation.allowed) {
          return reply.status(403).send({ 
            error: 'Forbidden: Insufficient permissions',
            message: scopeValidation.reason
          });
        }

        // Attach user info to request for later middleware
        (request as any).user = {
          id: verifyResult.user!.id,
          email: verifyResult.user!.email,
        };
      } catch (error) {
        request.log.error({ err: error }, 'Global scope validation failed');
        return reply.status(500).send({ error: 'Internal server error' });
      }
    });

    // Register routes with standardized prefixes
    // Authentication routes (no prefix - root level)
    await fastify.register(authRoutes, { prefix: '/' });

    // Admin routes
    await fastify.register(webhookRoutes, { prefix: '/admin/api' }); // Admin webhook routes: /admin/api/webhooks

    // User API routes
    await fastify.register(userWebhooksRoutes, { prefix: '/api' }); // User webhook routes: /api/webhooks
    await fastify.register(userAliasRoutes, { prefix: '/api' }); // User alias routes: /api/aliases
    await fastify.register(domainsRoutes, { prefix: '/api' }); // Domain routes: /api/domains (was /api/config)
    await fastify.register(logsRoutes, { prefix: '/api' }); // Logs routes: /api/logs
    await fastify.register(dashboardRoutes, { prefix: '/api' }); // Dashboard routes: /api/dashboard/*
    await fastify.register(templateRoutes, { prefix: '/api' }); // Template routes: /api/templates/*
    await fastify.register(billingRoutes, { prefix: '/api/billing' }); // Billing routes: /api/billing/*
    await fastify.register(apiKeyRoutes, { prefix: '/api' }); // API key routes: /api/api-keys
    await fastify.register(profileRoutes, { prefix: '/api' }); // Profile routes: /api/profile

    // Email processing routes
    await fastify.register(emailRoutes, { prefix: '/api/email' });

    // Webhook routes (no authentication required)
    await fastify.register(mollieWebhookRoutes, { prefix: '/api/webhooks/mollie' });

    // Enhanced health check endpoint
    fastify.get('/health', async () => {
      const dbHealthy = await checkDatabaseHealth();
      return {
        status: dbHealthy ? 'ok' : 'degraded',
        timestamp: new Date().toISOString(),
        database: dbHealthy ? 'connected' : 'disconnected'
      };
    });

    // Serve Vue SPA for all non-API routes (must be registered last)
    fastify.get('/', async (_, reply) => {
      return reply.sendFile('index.html');
    });

    // Catch-all route for Vue Router (SPA) - MUST BE LAST
    fastify.setNotFoundHandler(async (request, reply) => {
      // Skip API routes and static files - let them return 404
      if (request.url.startsWith('/api') ||
          request.url.startsWith('/docs') ||
          request.url.startsWith('/health') ||
          request.url.startsWith('/admin') ||
          request.url.endsWith('.png') ||
          request.url.endsWith('.jpg') ||
          request.url.endsWith('.jpeg') ||
          request.url.endsWith('.gif') ||
          request.url.endsWith('.svg') ||
          request.url.endsWith('.ico') ||
          request.url.endsWith('.js') ||
          request.url.endsWith('.css') ||
          request.url.endsWith('.json')) {
        return reply.code(404).send({ statusCode: 404, error: 'Not Found', message: 'Route not found' });
      }

      // For all other routes, serve the Vue SPA
      return reply.sendFile('index.html');
    });

    // Start server
    const address = await fastify.listen({
      port: env.PORT,
      host: env.HOST,
    });

    fastify.log.info(`🚀 EU Email Webhook Service running at ${address}`);

    // Start verification worker after server is running
    verificationWorker.start();
    fastify.log.info('✅ Automatic domain verification worker started');

    // Start usage reset service
    usageResetService.start();
    fastify.log.info('✅ Usage reset service started');

  } catch (error) {
    fastify.log.error(error);
    process.exit(1);
  }
}

// Graceful shutdown with database cleanup and worker stop
process.on('SIGTERM', async () => {
  fastify.log.info('Received SIGTERM, shutting down gracefully');
  verificationWorker.stop();
  usageResetService.stop();
  await fastify.close();
  await disconnectDatabase();
  process.exit(0);
});

process.on('SIGINT', async () => {
  fastify.log.info('Received SIGINT, shutting down gracefully');
  verificationWorker.stop();
  usageResetService.stop();
  await fastify.close();
  await disconnectDatabase();
  process.exit(0);
});

start();